const express = require('express');
const router = express.Router();
const ReportModel = require('../models/ReportModel');
const { validate, schemas } = require('../middleware/validation');
const { asyncHandler } = require('../middleware/errorHandler');

// GET /api/reports/all - Get all reports without pagination
router.get('/all', asyncHandler(async (req, res) => {
  const { month, voucherType, costCenter, search } = req.query;

  const filters = {};
  if (month) filters.month = month;
  if (voucherType) filters.voucherTypeName = voucherType;
  if (costCenter) filters.costCenter = costCenter;
  if (search) filters.search = search;

  const data = await ReportModel.findAll(filters);
  const total = await ReportModel.count(filters);

  res.json({
    success: true,
    data,
    total
  });
}));

// GET /api/reports - Get all reports with filtering and pagination
router.get('/', validate(schemas.queryParams, 'query'), asyncHandler(async (req, res) => {
  const { page, limit, month, voucherType, costCenter, search } = req.query;

  const filters = {};
  if (month) filters.month = month;
  if (voucherType) filters.voucherTypeName = voucherType;
  if (costCenter) filters.costCenter = costCenter;
  if (search) filters.search = search;

  const offset = (page - 1) * limit;
  const pagination = { limit, offset };

  const data = await ReportModel.findAll(filters, pagination);
  const total = await ReportModel.count(filters);

  res.json({
    success: true,
    data,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  });
}));

// GET /api/reports/:id - Get single report
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const report = await ReportModel.findById(id);

  if (!report) {
    return res.status(404).json({
      success: false,
      message: 'Report not found'
    });
  }

  res.json({
    success: true,
    data: report
  });
}));

// POST /api/reports - Create new report
router.post('/', validate(schemas.report), asyncHandler(async (req, res) => {
  const reportData = req.body;

  // Check if voucher number already exists
  const existing = await ReportModel.findByVoucherNumber(reportData.voucherNumber);
  if (existing) {
    return res.status(409).json({
      success: false,
      message: `Report with voucher number '${reportData.voucherNumber}' already exists`
    });
  }

  const report = await ReportModel.create(reportData);

  res.status(201).json({
    success: true,
    data: report,
    message: 'Report created successfully'
  });
}));

// PUT /api/reports/:id - Update report
router.put('/:id', validate(schemas.report), asyncHandler(async (req, res) => {
  const { id } = req.params;
  const reportData = req.body;

  const existing = await ReportModel.findById(id);
  if (!existing) {
    return res.status(404).json({
      success: false,
      message: 'Report not found'
    });
  }

  // Check if another report with the same voucher number exists (excluding current)
  const duplicate = await ReportModel.findByVoucherNumber(reportData.voucherNumber);
  if (duplicate && duplicate.id !== parseInt(id)) {
    return res.status(409).json({
      success: false,
      message: `Report with voucher number '${reportData.voucherNumber}' already exists`
    });
  }

  const updatedReport = await ReportModel.update(id, reportData);

  res.json({
    success: true,
    data: updatedReport,
    message: 'Report updated successfully'
  });
}));

// DELETE /api/reports/:id - Delete report
router.delete('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const existing = await ReportModel.findById(id);
  if (!existing) {
    return res.status(404).json({
      success: false,
      message: 'Report not found'
    });
  }

  await ReportModel.delete(id);

  res.json({
    success: true,
    message: 'Report deleted successfully'
  });
}));

// POST /api/reports/bulk - Bulk create/update reports
router.post('/bulk', validate(schemas.bulkReports), asyncHandler(async (req, res) => {
  const { data: reportsData } = req.body;

  if (!Array.isArray(reportsData) || reportsData.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'Reports data array is required and cannot be empty'
    });
  }

  const result = await ReportModel.bulkCreateOrUpdate(reportsData);

  res.json({
    success: true,
    data: result,
    message: `Bulk operation completed: ${result.imported} imported, ${result.updated} updated`
  });
}));

// GET /api/reports/filters/months - Get unique months
router.get('/filters/months', asyncHandler(async (req, res) => {
  const months = await ReportModel.getUniqueMonths();

  res.json({
    success: true,
    data: months
  });
}));

// GET /api/reports/summary/monthly - Get monthly summary
router.get('/summary/monthly', asyncHandler(async (req, res) => {
  const summary = await ReportModel.getReportsMonthlySummary();

  res.json({
    success: true,
    data: summary
  });
}));

// GET /api/reports/month/:month - Get reports by month
router.get('/month/:month', asyncHandler(async (req, res) => {
  const { month } = req.params;
  const reports = await ReportModel.findByMonth(month);

  res.json({
    success: true,
    data: reports
  });
}));

module.exports = router;
