const BaseModel = require('./BaseModel');

class ReportModel extends BaseModel {
  constructor() {
    super('reports');
  }

  getSearchableFields() {
    return ['voucherNumber', 'voucherTypeName', 'voucherNarration', 'costCenter', 'ledgerName'];
  }

  getDefaultOrderBy() {
    return 'createdAt DESC';
  }

  // Find reports by month
  findByMonth(month) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM ${this.tableName} WHERE month = ? ORDER BY createdAt DESC`;
      this.db.all(query, [month], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  // Find reports by voucher number
  findByVoucherNumber(voucherNumber) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM ${this.tableName} WHERE voucherNumber = ?`;
      this.db.get(query, [voucherNumber], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row || null);
        }
      });
    });
  }

  // Get unique months from reports
  getUniqueMonths() {
    return new Promise((resolve, reject) => {
      const query = `SELECT DISTINCT month FROM ${this.tableName} WHERE month IS NOT NULL AND month != '' ORDER BY month`;
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve((rows || []).map(row => row.month));
        }
      });
    });
  }

  // Get reports summary by month
  getReportsMonthlySummary() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT
          month,
          COUNT(*) as reportCount,
          SUM(ledgerAmount) as totalLedgerAmount,
          SUM(costAllocationAmount) as totalCostAllocationAmount,
          COUNT(DISTINCT costCenter) as uniqueCostCenters,
          COUNT(DISTINCT voucherTypeName) as uniqueVoucherTypes
        FROM ${this.tableName}
        GROUP BY month
        ORDER BY month
      `;
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  // Bulk create/update reports
  bulkCreateOrUpdate(dataArray) {
    return new Promise((resolve, reject) => {
      if (!Array.isArray(dataArray) || dataArray.length === 0) {
        resolve({ imported: 0, updated: 0 });
        return;
      }

      const timestamp = new Date().toISOString();
      let imported = 0;
      let updated = 0;
      let completed = 0;
      let hasError = false;

      this.db.run('BEGIN TRANSACTION', (err) => {
        if (err) {
          reject(err);
          return;
        }

        const processItem = (item) => {
          // Check if report with same voucher number exists
          const checkQuery = `SELECT id FROM ${this.tableName} WHERE voucherNumber = ?`;
          this.db.get(checkQuery, [item.voucherNumber], (checkErr, existing) => {
            if (checkErr) {
              if (!hasError) {
                hasError = true;
                this.db.run('ROLLBACK', () => {
                  reject(checkErr);
                });
              }
              return;
            }

            if (existing) {
              // Update existing record
              const updateQuery = `
                UPDATE ${this.tableName}
                SET voucherDate = ?, voucherTypeName = ?, voucherNarration = ?, month = ?,
                    costCenter = ?, ledgerName = ?, categoryName = ?, costAllocationCostCentre = ?,
                    ledgerAmount = ?, costAllocationAmount = ?, ledgerAmountDrCr = ?, updatedAt = ?
                WHERE id = ?
              `;
              this.db.run(updateQuery, [
                item.voucherDate,
                item.voucherTypeName,
                item.voucherNarration,
                item.month,
                item.costCenter,
                item.ledgerName,
                item.categoryName,
                item.costAllocationCostCentre,
                item.ledgerAmount,
                item.costAllocationAmount,
                item.ledgerAmountDrCr || 'Dr',
                timestamp,
                existing.id
              ], (updateErr) => {
                if (updateErr) {
                  if (!hasError) {
                    hasError = true;
                    this.db.run('ROLLBACK', () => {
                      reject(updateErr);
                    });
                  }
                  return;
                }
                updated++;
                completed++;
                checkCompletion();
              });
            } else {
              // Insert new record
              const insertQuery = `
                INSERT INTO ${this.tableName} (
                  voucherDate, voucherNumber, voucherTypeName, voucherNarration, month,
                  costCenter, ledgerName, categoryName, costAllocationCostCentre,
                  ledgerAmount, costAllocationAmount, ledgerAmountDrCr, createdAt, updatedAt
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
              `;
              this.db.run(insertQuery, [
                item.voucherDate,
                item.voucherNumber,
                item.voucherTypeName,
                item.voucherNarration,
                item.month,
                item.costCenter,
                item.ledgerName,
                item.categoryName,
                item.costAllocationCostCentre,
                item.ledgerAmount,
                item.costAllocationAmount,
                item.ledgerAmountDrCr || 'Dr',
                timestamp,
                timestamp
              ], (insertErr) => {
                if (insertErr) {
                  if (!hasError) {
                    hasError = true;
                    this.db.run('ROLLBACK', () => {
                      reject(insertErr);
                    });
                  }
                  return;
                }
                imported++;
                completed++;
                checkCompletion();
              });
            }
          });
        };

        const checkCompletion = () => {
          if (completed === dataArray.length && !hasError) {
            this.db.run('COMMIT', (commitErr) => {
              if (commitErr) {
                reject(commitErr);
              } else {
                resolve({ imported, updated });
              }
            });
          }
        };

        // Process all items
        dataArray.forEach(processItem);
      });
    });
  }
}

module.exports = ReportModel;
