import React, { useState } from "react";
import { BrowserRouter as Router, Routes, Route, useLocation } from "react-router-dom";
import Dashboard from "./components/Dashboard";
import CostCenter from "./components/CostCenter";
import Salary from "./components/Salary";
import Tally from "./components/Tally";
import ResourceUtilization from "./components/ResourceUtilization";
import ResourceCost from "./components/ResourceCost";
import EmployeeDiscrepancy from "./components/EmployeeDiscrepancy";
import ReportGeneration from "./components/ReportGeneration";

import { ThemeProvider } from "@mui/material/styles";
import theme from "./theme";
import AppLayout from "./components/layout/AppLayout";
import { AnimatePresence } from "framer-motion";
import { ToastProvider } from "./components/common/ToastProvider";

function AppContent() {
  // Month state for components that need it
  const [selectedMonth] = useState("January");
  const location = useLocation();

  return (
    <AnimatePresence mode="wait">
      <Routes location={location} key={location.pathname}>
        <Route path="/" element={<Dashboard />} />
        <Route path="/cost-center" element={<CostCenter selectedMonth={selectedMonth} />} />
        <Route path="/salary" element={<Salary />} />
        <Route path="/tally" element={<Tally selectedMonth={selectedMonth} />} />
        <Route path="/resource-utilization" element={<ResourceUtilization />} />
        <Route path="/resource-cost" element={<ResourceCost />} />
        <Route path="/employee-discrepancy" element={<EmployeeDiscrepancy />} />
        <Route path="/report-generation" element={<ReportGeneration />} />

      </Routes>
    </AnimatePresence>
  );
}

function App() {
  return (
    <ThemeProvider theme={theme}>
      <ToastProvider>
        <Router>
          <AppLayout>
            <AppContent />
          </AppLayout>
        </Router>
      </ToastProvider>
    </ThemeProvider>
  );
}

export default App;
