// src/components/ReportGeneration.js
import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  Container,
  Typography,
  Card,
  CardContent,
  Box,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormLabel,
  Divider,
  Chip,
  Paper,
} from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { motion } from "framer-motion";
import * as XLSX from "xlsx";
import {
  loadCostCenterMappings,
  loadCostCenterData,
  loadSalaryData,
  createReport,
  loadReportsData,
} from "../db";
import {
  applyCostCenterMapping,
  getCostCenterCategoryName,
  getCostCenterLedgerName,
  getUniqueCostCenters,
} from "../utils/costCenterMappings";
import { useToast } from "../components/common/ToastProvider";

const ReportGeneration = () => {
  // Form state
  const [formData, setFormData] = useState({
    voucherDate: new Date(),
    voucherNumber: "",
    voucherTypeName: "",
    voucherNarration: "",
    month: "",
    costCenter: "",
    ledgerAmountDrCr: "Dr",
  });

  // Auto-populated fields
  const [autoFields, setAutoFields] = useState({
    ledgerName: "",
    categoryName: "",
    costAllocationCostCentre: "",
    ledgerAmount: 0,
    costAllocationAmount: 0,
  });

  // Data state
  const [costCenterMappings, setCostCenterMappings] = useState({});
  const [uniqueCostCenters, setUniqueCostCenters] = useState([]);
  const [uniqueMonths, setUniqueMonths] = useState([]);
  const [tallyData, setTallyData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  // Modal state
  const [previewModalOpen, setPreviewModalOpen] = useState(false);
  const [generatedReport, setGeneratedReport] = useState(null);

  const { showToast } = useToast();

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      setLoading(true);
      try {
        // Load cost center mappings
        const mappings = await loadCostCenterMappings();
        const mappingObject = {};
        mappings.forEach(mapping => {
          mappingObject[mapping.originalName] = {
            alternateName: mapping.alternateName,
            categoryName: mapping.categoryName || '',
            ledgerName: mapping.ledgerName || ''
          };
        });
        setCostCenterMappings(mappingObject);

        // Load cost center data to get unique cost centers and months
        const ccData = await loadCostCenterData();
        const costCenters = getUniqueCostCenters(ccData, 'costCenter');
        setUniqueCostCenters(costCenters);

        const months = ccData
          .filter(record => record.month && record.month.trim() !== "")
          .map(record => record.month.trim());
        const uniqueMonthsList = [...new Set(months)];
        setUniqueMonths(uniqueMonthsList);

      } catch (error) {
        console.error('Error loading initial data:', error);
        showToast('Failed to load initial data', 'error');
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();
  }, [showToast]);

  // Calculate tally data when month and cost center change
  const calculateTallyData = useCallback(async (selectedMonth, selectedCostCenter) => {
    if (!selectedMonth || !selectedCostCenter) {
      setAutoFields(prev => ({
        ...prev,
        ledgerAmount: 0,
        costAllocationAmount: 0,
      }));
      return;
    }

    try {
      const ccData = await loadCostCenterData();
      const salaryData = await loadSalaryData();

      // Filter cost center data for the selected month and cost center
      const filteredCC = ccData.filter(record =>
        record.month && record.month.trim() === selectedMonth &&
        record.costCenter && record.costCenter.trim() === selectedCostCenter
      );

      if (filteredCC.length === 0) {
        setAutoFields(prev => ({
          ...prev,
          ledgerAmount: 0,
          costAllocationAmount: 0,
        }));
        return;
      }

      // Calculate employee total hours
      const employeeHours = new Map();
      filteredCC.forEach(record => {
        const empName = record.name?.trim().toLowerCase();
        if (!empName) return;
        const hrs = parseFloat(record.hoursWorked || record.hours || 0);
        employeeHours.set(empName, (employeeHours.get(empName) || 0) + hrs);
      });

      // Pre-index salary data for faster lookup
      const salaryMap = new Map();
      salaryData.forEach(record => {
        if (record.payrollMonth && record.payrollMonth.trim() === selectedMonth && record.name) {
          const empName = record.name.trim().toLowerCase();
          salaryMap.set(empName, {
            gross: parseFloat(record.gross || 0),
            pf: parseFloat(record.pf || 0)
          });
        }
      });

      // Calculate totals for the selected cost center
      let totalGross = 0;
      let totalPF = 0;

      filteredCC.forEach(record => {
        const hrs = parseFloat(record.hoursWorked || record.hours || 0);
        if (hrs <= 0) return;

        const empName = record.name?.trim().toLowerCase();
        if (!empName) return;

        const totalHours = employeeHours.get(empName) || 0;
        const salary = salaryMap.get(empName);

        if (!salary || totalHours <= 0) return;

        // Compute cost for this record
        const cost = {
          gross: (salary.gross / totalHours) * hrs,
          pf: (salary.pf / totalHours) * hrs
        };

        totalGross += cost.gross;
        totalPF += cost.pf;
      });

      setAutoFields(prev => ({
        ...prev,
        ledgerAmount: totalGross,
        costAllocationAmount: totalGross, // Assuming same as ledger amount
      }));

    } catch (error) {
      console.error('Error calculating tally data:', error);
      showToast('Failed to calculate tally data', 'error');
    }
  }, [showToast]);

  // Handle cost center selection
  const handleCostCenterChange = useCallback((costCenter) => {
    setFormData(prev => ({ ...prev, costCenter }));

    if (costCenter && costCenterMappings[costCenter]) {
      const mapping = costCenterMappings[costCenter];
      setAutoFields(prev => ({
        ...prev,
        ledgerName: mapping.ledgerName || '',
        categoryName: mapping.categoryName || '',
        costAllocationCostCentre: mapping.categoryName || costCenter,
      }));
    } else {
      setAutoFields(prev => ({
        ...prev,
        ledgerName: '',
        categoryName: '',
        costAllocationCostCentre: costCenter || '',
      }));
    }

    // Recalculate tally data
    if (formData.month && costCenter) {
      calculateTallyData(formData.month, costCenter);
    }
  }, [costCenterMappings, formData.month, calculateTallyData]);

  // Handle month selection
  const handleMonthChange = useCallback((month) => {
    setFormData(prev => ({ ...prev, month }));

    // Recalculate tally data
    if (formData.costCenter && month) {
      calculateTallyData(month, formData.costCenter);
    }
  }, [formData.costCenter, calculateTallyData]);

  // Form validation
  const validateForm = useCallback(() => {
    const newErrors = {};

    if (!formData.voucherDate) newErrors.voucherDate = 'Voucher date is required';
    if (!formData.voucherNumber.trim()) newErrors.voucherNumber = 'Voucher number is required';
    if (!formData.voucherTypeName.trim()) newErrors.voucherTypeName = 'Voucher type name is required';
    if (!formData.month) newErrors.month = 'Month selection is required';
    if (!formData.costCenter) newErrors.costCenter = 'Cost center selection is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    if (!validateForm()) {
      showToast('Please fix the form errors', 'error');
      return;
    }

    setSubmitting(true);
    try {
      const reportData = {
        voucherDate: formData.voucherDate.toISOString().split('T')[0],
        voucherNumber: formData.voucherNumber.trim(),
        voucherTypeName: formData.voucherTypeName.trim(),
        voucherNarration: formData.voucherNarration.trim(),
        month: formData.month,
        costCenter: formData.costCenter,
        ledgerName: autoFields.ledgerName,
        categoryName: autoFields.categoryName,
        costAllocationCostCentre: autoFields.costAllocationCostCentre,
        ledgerAmount: autoFields.ledgerAmount,
        costAllocationAmount: autoFields.costAllocationAmount,
        ledgerAmountDrCr: formData.ledgerAmountDrCr,
      };

      const createdReport = await createReport(reportData);
      setGeneratedReport(createdReport);
      setPreviewModalOpen(true);
      showToast('Report generated successfully!', 'success');

      // Reset form
      setFormData({
        voucherDate: new Date(),
        voucherNumber: "",
        voucherTypeName: "",
        voucherNarration: "",
        month: "",
        costCenter: "",
        ledgerAmountDrCr: "Dr",
      });
      setAutoFields({
        ledgerName: "",
        categoryName: "",
        costAllocationCostCentre: "",
        ledgerAmount: 0,
        costAllocationAmount: 0,
      });

    } catch (error) {
      console.error('Error creating report:', error);
      showToast(error.message || 'Failed to create report', 'error');
    } finally {
      setSubmitting(false);
    }
  }, [formData, autoFields, validateForm, showToast]);

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Container sx={{ padding: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Report Generation
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
            Generate comprehensive reports by combining manual inputs with existing cost center and tally data.
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Grid container spacing={3}>
              {/* Manual Input Fields */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Manual Input Fields
                    </Typography>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <DatePicker
                          label="Voucher Date *"
                          value={formData.voucherDate}
                          onChange={(date) => setFormData(prev => ({ ...prev, voucherDate: date }))}
                          slotProps={{
                            textField: {
                              fullWidth: true,
                              error: !!errors.voucherDate,
                              helperText: errors.voucherDate
                            }
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="Voucher Number *"
                          value={formData.voucherNumber}
                          onChange={(e) => setFormData(prev => ({ ...prev, voucherNumber: e.target.value }))}
                          fullWidth
                          error={!!errors.voucherNumber}
                          helperText={errors.voucherNumber}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="Voucher Type Name *"
                          value={formData.voucherTypeName}
                          onChange={(e) => setFormData(prev => ({ ...prev, voucherTypeName: e.target.value }))}
                          fullWidth
                          error={!!errors.voucherTypeName}
                          helperText={errors.voucherTypeName}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <FormControl fullWidth>
                          <FormLabel component="legend">Ledger Amount Dr/Cr</FormLabel>
                          <RadioGroup
                            row
                            value={formData.ledgerAmountDrCr}
                            onChange={(e) => setFormData(prev => ({ ...prev, ledgerAmountDrCr: e.target.value }))}
                          >
                            <FormControlLabel value="Dr" control={<Radio />} label="Dr" />
                            <FormControlLabel value="Cr" control={<Radio />} label="Cr" />
                          </RadioGroup>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          label="Voucher Narration"
                          value={formData.voucherNarration}
                          onChange={(e) => setFormData(prev => ({ ...prev, voucherNarration: e.target.value }))}
                          fullWidth
                          multiline
                          rows={3}
                          placeholder="Enter detailed description of the voucher..."
                        />
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>

              {/* Data Selection Fields */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Data Selection
                    </Typography>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <FormControl fullWidth error={!!errors.month}>
                          <InputLabel>Month Selection *</InputLabel>
                          <Select
                            value={formData.month}
                            onChange={(e) => handleMonthChange(e.target.value)}
                            label="Month Selection *"
                          >
                            {uniqueMonths.map((month) => (
                              <MenuItem key={month} value={month}>
                                {month}
                              </MenuItem>
                            ))}
                          </Select>
                          {errors.month && (
                            <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                              {errors.month}
                            </Typography>
                          )}
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Autocomplete
                          options={uniqueCostCenters}
                          value={formData.costCenter}
                          onChange={(_, value) => handleCostCenterChange(value || '')}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Cost Center Selection *"
                              error={!!errors.costCenter}
                              helperText={errors.costCenter}
                            />
                          )}
                          isOptionEqualToValue={(option, value) => option === value}
                        />
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>

              {/* Auto-populated Fields */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Auto-populated Fields
                    </Typography>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="Ledger Name"
                          value={autoFields.ledgerName}
                          fullWidth
                          InputProps={{ readOnly: true }}
                          helperText="Auto-populated from cost center mapping"
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="Category Name"
                          value={autoFields.categoryName}
                          fullWidth
                          InputProps={{ readOnly: true }}
                          helperText="Auto-populated from cost center mapping"
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="Cost Allocation for - Cost Centre"
                          value={autoFields.costAllocationCostCentre}
                          fullWidth
                          InputProps={{ readOnly: true }}
                          helperText="Auto-populated from cost center mapping"
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="Ledger Amount"
                          value={autoFields.ledgerAmount.toFixed(2)}
                          fullWidth
                          InputProps={{ readOnly: true }}
                          helperText="Auto-populated from tally output data"
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="Cost Allocation for - Amount"
                          value={autoFields.costAllocationAmount.toFixed(2)}
                          fullWidth
                          InputProps={{ readOnly: true }}
                          helperText="Auto-populated from tally output data"
                        />
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>

              {/* Action Buttons */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                  <Button
                    variant="outlined"
                    onClick={() => {
                      setFormData({
                        voucherDate: new Date(),
                        voucherNumber: "",
                        voucherTypeName: "",
                        voucherNarration: "",
                        month: "",
                        costCenter: "",
                        ledgerAmountDrCr: "Dr",
                      });
                      setAutoFields({
                        ledgerName: "",
                        categoryName: "",
                        costAllocationCostCentre: "",
                        ledgerAmount: 0,
                        costAllocationAmount: 0,
                      });
                      setErrors({});
                    }}
                  >
                    Reset Form
                  </Button>
                  <Button
                    variant="contained"
                    onClick={handleSubmit}
                    disabled={submitting}
                    startIcon={submitting ? <CircularProgress size={20} /> : null}
                  >
                    {submitting ? 'Generating...' : 'Generate Report'}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          )}

          {/* Report Preview Modal */}
          <Dialog
            open={previewModalOpen}
            onClose={() => setPreviewModalOpen(false)}
            maxWidth="md"
            fullWidth
            PaperProps={{
              component: motion.div,
              initial: { opacity: 0, scale: 0.9 },
              animate: { opacity: 1, scale: 1 },
              exit: { opacity: 0, scale: 0.9 },
              transition: { duration: 0.2 },
            }}
          >
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">Generated Report Preview</Typography>
                <Chip
                  label="Success"
                  color="success"
                  size="small"
                />
              </Box>
            </DialogTitle>
            <DialogContent>
              {generatedReport && (
                <Paper sx={{ p: 3, bgcolor: 'grey.50' }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Typography variant="h6" gutterBottom color="primary">
                        Report Details
                      </Typography>
                      <Divider sx={{ mb: 2 }} />
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Voucher Date:</Typography>
                      <Typography variant="body1">{new Date(generatedReport.voucherDate).toLocaleDateString()}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Voucher Number:</Typography>
                      <Typography variant="body1">{generatedReport.voucherNumber}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Voucher Type:</Typography>
                      <Typography variant="body1">{generatedReport.voucherTypeName}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Month:</Typography>
                      <Typography variant="body1">{generatedReport.month}</Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="body2" color="text.secondary">Voucher Narration:</Typography>
                      <Typography variant="body1">{generatedReport.voucherNarration || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Divider sx={{ my: 1 }} />
                      <Typography variant="h6" gutterBottom color="primary">
                        Cost Center Information
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Cost Center:</Typography>
                      <Typography variant="body1">{generatedReport.costCenter}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Ledger Name:</Typography>
                      <Typography variant="body1">{generatedReport.ledgerName || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Category Name:</Typography>
                      <Typography variant="body1">{generatedReport.categoryName || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Cost Allocation Centre:</Typography>
                      <Typography variant="body1">{generatedReport.costAllocationCostCentre || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Divider sx={{ my: 1 }} />
                      <Typography variant="h6" gutterBottom color="primary">
                        Financial Information
                      </Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">Ledger Amount:</Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                        {generatedReport.ledgerAmount?.toFixed(2)} ({generatedReport.ledgerAmountDrCr})
                      </Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">Cost Allocation Amount:</Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                        {generatedReport.costAllocationAmount?.toFixed(2)}
                      </Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">Report ID:</Typography>
                      <Typography variant="body1">#{generatedReport.id}</Typography>
                    </Grid>
                  </Grid>
                </Paper>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setPreviewModalOpen(false)}>
                Close
              </Button>
              <Button
                variant="contained"
                onClick={() => {
                  if (generatedReport) {
                    // Export to Excel
                    const wb = XLSX.utils.book_new();
                    const reportData = [
                      ['Field', 'Value'],
                      ['Voucher Date', new Date(generatedReport.voucherDate).toLocaleDateString()],
                      ['Voucher Number', generatedReport.voucherNumber],
                      ['Voucher Type Name', generatedReport.voucherTypeName],
                      ['Voucher Narration', generatedReport.voucherNarration || ''],
                      ['Month', generatedReport.month],
                      ['Cost Center', generatedReport.costCenter],
                      ['Ledger Name', generatedReport.ledgerName || ''],
                      ['Category Name', generatedReport.categoryName || ''],
                      ['Cost Allocation Centre', generatedReport.costAllocationCostCentre || ''],
                      ['Ledger Amount', generatedReport.ledgerAmount?.toFixed(2) || '0.00'],
                      ['Ledger Amount Dr/Cr', generatedReport.ledgerAmountDrCr],
                      ['Cost Allocation Amount', generatedReport.costAllocationAmount?.toFixed(2) || '0.00'],
                      ['Report ID', generatedReport.id],
                      ['Created At', new Date(generatedReport.createdAt || Date.now()).toLocaleString()],
                    ];
                    const ws = XLSX.utils.aoa_to_sheet(reportData);
                    XLSX.utils.book_append_sheet(wb, ws, 'Report');
                    XLSX.writeFile(wb, `Report_${generatedReport.voucherNumber}_${generatedReport.month}.xlsx`);
                    showToast('Report exported successfully!', 'success');
                  }
                }}
              >
                Export to Excel
              </Button>
            </DialogActions>
          </Dialog>
        </Container>
      </motion.div>
    </LocalizationProvider>
  );
};

export default ReportGeneration;
