// src/db.js - API Service Layer for SQLite Backend
import axios from 'axios';
// Import data cache utility
import { getCachedData } from './utils/dataCache';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Store names for backward compatibility
export const STORE_NAMES = {
  COST_CENTER: "cost_center",
  SALARY: "salary",
  SALARY_MASTER: "salary_master",
  PAGINATION_STATE: "pagination_state",
  RESOURCE_UTILIZATION: "resource_utilization",
  RESOURCE_COST: "resource_cost",
  EMPLOYEE_DISCREPANCY: "employee_discrepancy",
  COST_CENTER_MAPPINGS: "cost_center_mappings",
  REPORTS: "reports",
};

// API request interceptors for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);

    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.message || 'Server error occurred';
      throw new Error(message);
    } else if (error.request) {
      // Request was made but no response received
      throw new Error('Unable to connect to server. Please check your connection.');
    } else {
      // Something else happened
      throw new Error('An unexpected error occurred');
    }
  }
);

// Initialize database (now just a health check)
export const initDB = async () => {
  try {
    const response = await api.get('/database/status');
    console.log('Database connection verified:', response.data);
    return true;
  } catch (error) {
    console.error('Database connection failed:', error);
    throw error;
  }
};

// Helper function to notify listeners of DB changes
export const notifyDBChange = () => {
  const event = new CustomEvent("dbChange");
  window.dispatchEvent(event);
};

// Map store names to API endpoints
const getEndpoint = (storeName) => {
  const endpointMap = {
    [STORE_NAMES.COST_CENTER]: '/cost-center',
    [STORE_NAMES.SALARY]: '/salary',
    [STORE_NAMES.SALARY_MASTER]: '/salary-master',
    [STORE_NAMES.RESOURCE_UTILIZATION]: '/resource-utilization',
    [STORE_NAMES.RESOURCE_COST]: '/resource-cost',
    [STORE_NAMES.EMPLOYEE_DISCREPANCY]: '/employee-discrepancy',
    [STORE_NAMES.PAGINATION_STATE]: '/pagination-state',
    [STORE_NAMES.COST_CENTER_MAPPINGS]: '/cost-center-mappings',
    [STORE_NAMES.REPORTS]: '/reports',
  };
  return endpointMap[storeName];
};

// Helper function to clean data for API submission
const cleanDataForAPI = (storeName, data) => {
  if (storeName === STORE_NAMES.SALARY) {
    // For salary data, only send required fields to the API
    return data.map(item => ({
      payrollMonth: item.payrollMonth,
      name: item.name,
      gross: item.gross,
      pf: item.pf || 0,
      salaryMaster: item.salaryMaster || '',
      source: item.source || 'manual'
    }));
  } else if (storeName === STORE_NAMES.COST_CENTER) {
    // For cost center data, only send required fields to the API
    return data.map(item => ({
      name: item.name,
      costCenter: item.costCenter,
      hoursWorked: item.hoursWorked,
      month: item.month,
      source: item.source || 'manual'
    }));
  }
  return data;
};

// Save data (bulk create/update)
export const saveData = async (storeName, data) => {
  try {
    const endpoint = getEndpoint(storeName);
    if (!endpoint) {
      throw new Error(`Unknown store name: ${storeName}`);
    }

    if (storeName === STORE_NAMES.PAGINATION_STATE) {
      // Handle pagination state differently
      throw new Error('Use savePaginationState for pagination data');
    }

    // Clean data before sending to API
    const cleanedData = cleanDataForAPI(storeName, data);

    // Use bulk endpoint for cost center and salary data
    const response = await api.post(`${endpoint}/bulk`, cleanedData);
    notifyDBChange();
    return response.data.success;
  } catch (error) {
    console.error(`Error saving data to ${storeName}:`, error);

    // Enhanced error handling for validation errors
    if (error.response && error.response.status === 400) {
      const errorData = error.response.data;
      if (errorData.errors && Array.isArray(errorData.errors)) {
        // Format validation errors for better user understanding
        const formattedErrors = errorData.errors.map(err => {
          const rowInfo = `Row ${err.index + 1}`;
          const fieldErrors = err.errors.map(fieldErr => `${fieldErr.field}: ${fieldErr.message}`).join(', ');
          return `${rowInfo} - ${fieldErrors}`;
        });
        throw new Error(`Validation failed:\n${formattedErrors.join('\n')}`);
      } else {
        throw new Error(errorData.message || 'Validation failed');
      }
    }

    throw error;
  }
};

// Load data
export const loadData = async (storeName) => {
  try {
    const endpoint = getEndpoint(storeName);
    if (!endpoint) {
      throw new Error(`Unknown store name: ${storeName}`);
    }

    if (storeName === STORE_NAMES.PAGINATION_STATE) {
      // Handle pagination state differently
      throw new Error('Use loadPaginationState for pagination data');
    }

    // For cost center and salary data, we need to load all records
    // Use a high limit to get all data in one request
    const params = {
      page: 1,
      limit: 10000 // High limit to get all records
    };

    const response = await api.get(endpoint, { params });
    return response.data.data || [];
  } catch (error) {
    console.error(`Error loading data from ${storeName}:`, error);
    throw error;
  }
};

// Specific data loading functions with caching
export const loadCostCenterData = async () => {
  return getCachedData(
    'cost-center-data',
    async () => {
      try {
        // Use the dedicated /all endpoint to get all cost center data
        const response = await api.get('/cost-center/all');
        return response.data.data || [];
      } catch (error) {
        console.error('Error loading cost center data:', error);
        throw error;
      }
    },
    5 * 60 * 1000 // 5 minutes cache
  );
};

export const loadSalaryData = async () => {
  return getCachedData(
    'salary-data',
    async () => {
      try {
        // Use the dedicated /all endpoint to get all salary data
        const response = await api.get('/salary/all');
        return response.data.data || [];
      } catch (error) {
        console.error('Error loading salary data:', error);
        throw error;
      }
    },
    5 * 60 * 1000 // 5 minutes cache
  );
};

export const loadSalaryMasterData = () => loadData(STORE_NAMES.SALARY_MASTER);

// Resource utilization data (computed from cost center data)
export const loadResourceUtilizationData = async (filters = {}) => {
  try {
    const response = await api.get('/resource-utilization', { params: filters });
    return response.data.data || [];
  } catch (error) {
    console.error('Error loading resource utilization data:', error);
    throw error;
  }
};

// Resource cost data (computed from cost center and salary data)
export const loadResourceCostData = async (filters = {}) => {
  try {
    const response = await api.get('/resource-cost', { params: filters });
    return response.data.data || [];
  } catch (error) {
    console.error('Error loading resource cost data:', error);
    throw error;
  }
};

// Employee discrepancy data (computed differences)
export const loadEmployeeDiscrepancyData = async (filters = {}) => {
  try {
    const response = await api.get('/employee-discrepancy', { params: filters });
    return response.data.data || {};
  } catch (error) {
    console.error('Error loading employee discrepancy data:', error);
    throw error;
  }
};

// Clear data from a store
export const clearData = async (storeName) => {
  try {
    const endpoint = getEndpoint(storeName);
    if (!endpoint) {
      throw new Error(`Unknown store name: ${storeName}`);
    }

    if (storeName === STORE_NAMES.PAGINATION_STATE) {
      // Clear all pagination states
      await api.post('/pagination-state/reset');
    } else {
      // Clear specific store data
      await api.delete(`${endpoint}/clear`);
    }

    notifyDBChange();
    return true;
  } catch (error) {
    console.error(`Error clearing data from ${storeName}:`, error);
    throw error;
  }
};

// Save pagination state
export const savePaginationState = async (componentName, state) => {
  try {
    console.log('Saving pagination state:', { componentName, state });
    const response = await api.post(`/pagination-state/${componentName}`, state);
    return response.data.success;
  } catch (error) {
    console.error(`Error saving pagination state for ${componentName}:`, error);
    console.error('Request data was:', { componentName, state });
    throw error;
  }
};

// Load pagination state
export const loadPaginationState = async (componentName) => {
  try {
    const response = await api.get(`/pagination-state/${componentName}`);
    return response.data.data || { currentPage: 1, itemsPerPage: 100 };
  } catch (error) {
    console.error(`Error loading pagination state for ${componentName}:`, error);
    // Return default values on error
    return { currentPage: 1, itemsPerPage: 100 };
  }
};

// Cost Center Mapping functions
export const loadCostCenterMappings = async () => {
  try {
    // Use a high limit to get all mappings without pagination issues
    const response = await api.get('/cost-center-mappings', {
      params: { limit: 10000 }
    });
    return response.data.data || [];
  } catch (error) {
    console.error('Error loading cost center mappings:', error);
    return [];
  }
};

export const saveCostCenterMappings = async (mappings) => {
  try {
    const response = await api.post('/cost-center-mappings/bulk', mappings);
    notifyDBChange();
    return response.data.success;
  } catch (error) {
    console.error('Error saving cost center mappings:', error);
    throw error;
  }
};

export const createCostCenterMapping = async (mapping) => {
  try {
    const response = await api.post('/cost-center-mappings', mapping);
    notifyDBChange();
    return response.data.data;
  } catch (error) {
    console.error('Error creating cost center mapping:', error);
    throw error;
  }
};

export const updateCostCenterMapping = async (id, mapping) => {
  try {
    const response = await api.put(`/cost-center-mappings/${id}`, mapping);
    notifyDBChange();
    return response.data.data;
  } catch (error) {
    console.error('Error updating cost center mapping:', error);
    throw error;
  }
};

export const deleteCostCenterMapping = async (id) => {
  try {
    const response = await api.delete(`/cost-center-mappings/${id}`);
    notifyDBChange();
    return response.data.success;
  } catch (error) {
    console.error('Error deleting cost center mapping:', error);
    throw error;
  }
};

// Report functions
export const loadReportsData = async (filters = {}) => {
  try {
    const response = await api.get('/reports/all', { params: filters });
    return response.data.data || [];
  } catch (error) {
    console.error('Error loading reports data:', error);
    throw error;
  }
};

export const loadReportsByMonth = async (month) => {
  try {
    const response = await api.get(`/reports/month/${month}`);
    return response.data.data || [];
  } catch (error) {
    console.error('Error loading reports by month:', error);
    throw error;
  }
};

export const createReport = async (reportData) => {
  try {
    const response = await api.post('/reports', reportData);
    notifyDBChange();
    return response.data.data;
  } catch (error) {
    console.error('Error creating report:', error);
    throw error;
  }
};

export const updateReport = async (id, reportData) => {
  try {
    const response = await api.put(`/reports/${id}`, reportData);
    notifyDBChange();
    return response.data.data;
  } catch (error) {
    console.error('Error updating report:', error);
    throw error;
  }
};

export const deleteReport = async (id) => {
  try {
    const response = await api.delete(`/reports/${id}`);
    notifyDBChange();
    return response.data.success;
  } catch (error) {
    console.error('Error deleting report:', error);
    throw error;
  }
};

export const bulkCreateReports = async (reportsData) => {
  try {
    const response = await api.post('/reports/bulk', { data: reportsData });
    notifyDBChange();
    return response.data.data;
  } catch (error) {
    console.error('Error bulk creating reports:', error);
    throw error;
  }
};

export const getReportsMonthlySummary = async () => {
  try {
    const response = await api.get('/reports/summary/monthly');
    return response.data.data || [];
  } catch (error) {
    console.error('Error loading reports monthly summary:', error);
    throw error;
  }
};
