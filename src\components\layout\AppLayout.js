import React, { useState } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  useTheme,
  useMediaQuery,
  Tooltip,
  Avatar,
  Divider,
  Badge
} from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import MenuIcon from '@mui/icons-material/Menu';
import DashboardIcon from '@mui/icons-material/Dashboard';
import BusinessIcon from '@mui/icons-material/Business';
import PaidIcon from '@mui/icons-material/Paid';
import CalculateIcon from '@mui/icons-material/Calculate';
import TimelineIcon from '@mui/icons-material/Timeline';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import CompareIcon from '@mui/icons-material/Compare';
import AssessmentIcon from '@mui/icons-material/Assessment';
import NotificationsIcon from '@mui/icons-material/Notifications';
import SearchIcon from '@mui/icons-material/Search';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { motion } from 'framer-motion';
import Breadcrumbs from '../common/Breadcrumbs';

const drawerWidth = 280;

const menuItems = [
  { path: '/', label: 'Dashboard', icon: <DashboardIcon /> },
  { path: '/cost-center', label: 'Cost Center', icon: <BusinessIcon /> },
  { path: '/salary', label: 'Salary', icon: <PaidIcon /> },
  { path: '/tally', label: 'Tally', icon: <CalculateIcon /> },
  { path: '/resource-utilization', label: 'Resource Utilization', icon: <TimelineIcon /> },
  { path: '/resource-cost', label: 'Resource Cost', icon: <MonetizationOnIcon /> },
  { path: '/employee-discrepancy', label: 'Employee Discrepancy', icon: <CompareIcon /> },
  { path: '/report-generation', label: 'Report Generation', icon: <AssessmentIcon /> },
];

export default function AppLayout({ children }) {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [mobileOpen, setMobileOpen] = useState(false);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  // Close mobile drawer when navigating
  const handleNavigation = (path) => {
    navigate(path);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  // Animation variants for menu items
  const menuItemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: (i) => ({
      opacity: 1,
      x: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
        ease: [0.6, -0.05, 0.01, 0.99],
      },
    }),
  };

  const drawer = (
    <Box sx={{ height: '100%', backgroundColor: theme.palette.background.paper, display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ p: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
          Financial Report
        </Typography>
      </Box>
      <Divider sx={{ mb: 2 }} />
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <List>
          {menuItems.map((item, index) => (
            <motion.div
              key={item.path}
              custom={index}
              initial="hidden"
              animate="visible"
              variants={menuItemVariants}
            >
              <Tooltip title={isTablet ? item.label : ''} placement="right" arrow>
                <ListItem
                  button
                  onClick={() => handleNavigation(item.path)}
                  sx={{
                    mx: 1,
                    borderRadius: 1,
                    mb: 0.5,
                    py: 1.5,
                    backgroundColor: location.pathname === item.path ? `${theme.palette.primary.light}20` : 'transparent',
                    '&:hover': {
                      backgroundColor: `${theme.palette.primary.light}10`,
                    },
                    transition: 'all 0.2s ease-in-out',
                  }}
                >
                  <ListItemIcon sx={{
                    color: location.pathname === item.path ? theme.palette.primary.main : 'inherit',
                    minWidth: isTablet ? 'auto' : 40,
                    mr: isTablet ? 0 : 2,
                  }}>
                    {item.icon}
                  </ListItemIcon>
                  {!isTablet && (
                    <ListItemText
                      primary={item.label}
                      sx={{
                        '& .MuiTypography-root': {
                          fontWeight: location.pathname === item.path ? 600 : 400,
                          color: location.pathname === item.path ? theme.palette.primary.main : 'inherit',
                        }
                      }}
                    />
                  )}
                </ListItem>
              </Tooltip>
            </motion.div>
          ))}
        </List>
      </Box>
      <Divider sx={{ mt: 2 }} />
      <Box sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: isTablet ? 'center' : 'flex-start' }}>
        <Avatar sx={{ width: 32, height: 32, mr: isTablet ? 0 : 2, bgcolor: theme.palette.primary.main }}>U</Avatar>
        {!isTablet && (
          <Box>
            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>User</Typography>
            <Typography variant="caption" color="text.secondary">Admin</Typography>
          </Box>
        )}
      </Box>
    </Box>
  );

  // Calculate drawer width based on screen size
  const actualDrawerWidth = isTablet ? 80 : drawerWidth;

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          width: { sm: `calc(100% - ${actualDrawerWidth}px)` },
          ml: { sm: `${actualDrawerWidth}px` },
          backgroundColor: 'white',
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Toolbar sx={{ justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2, display: { sm: 'none' } }}
            >
              <MenuIcon />
            </IconButton>
            <Typography variant="h6" noWrap component="div" color="primary">
              {menuItems.find(item => item.path === location.pathname)?.label || 'Dashboard'}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Tooltip title="Search">
              <IconButton color="inherit" sx={{ mx: 1 }}>
                <SearchIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Help">
              <IconButton color="inherit" sx={{ mx: 1 }}>
                <HelpOutlineIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Notifications">
              <IconButton color="inherit" sx={{ mx: 1 }}>
                <Badge badgeContent={3} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>
          </Box>
        </Toolbar>
      </AppBar>
      <Box
        component="nav"
        sx={{ width: { sm: actualDrawerWidth }, flexShrink: { sm: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: actualDrawerWidth,
              borderRight: `1px solid ${theme.palette.divider}`,
              transition: 'width 0.2s ease-in-out',
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: { xs: 2, sm: 3 },
          width: { sm: `calc(100% - ${actualDrawerWidth}px)` },
          backgroundColor: theme.palette.background.default,
          minHeight: '100vh',
          transition: 'width 0.2s ease-in-out, padding 0.2s ease-in-out',
        }}
      >
        <Toolbar />
        <Breadcrumbs />
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          style={{ width: '100%' }}
        >
          {children}
        </motion.div>
      </Box>
    </Box>
  );
}
